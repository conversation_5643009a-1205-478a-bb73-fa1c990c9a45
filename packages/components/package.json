{"devDependencies": {"@mass/api": "workspace:*", "@mass/icons": "workspace:*", "@mass/utils": "workspace:*", "@ozaco/cli": "^0.0.13", "@types/react": "^19.1.8", "react-i18next": "^15.6.0", "@tanstack/react-router": "^1.127.3", "react-slick": "^0.30.3", "@types/react-slick": "^0.23.13", "clsx": "^2.1.1", "motion": "^12.23.5", "react": "^19.1.0", "typescript": "^5.8.3", "@legendapp/state": "^3.0.0-beta.31", "@radix-ui/react-dialog": "^1.1.14", "@headlessui/react": "^2.2.4", "react-pdf": "^10.0.1", "react-hot-toast": "^2.5.2", "@radix-ui/react-select": "^2.2.5", "arktype": "^2.1.20", "react-day-picker": "^9.8.0"}, "exports": {"./dashboard": {"default": "./dist/dashboard.js", "source": "./src/dashboard/index.ts", "types": "./dist/dashboard.d.ts"}, "./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "files": ["dist"], "name": "@mass/components", "peerDependencies": {"@mass/api": "workspace:*", "@mass/icons": "workspace:*", "@mass/utils": "workspace:*", "react-i18next": "^15.6.0", "@tanstack/react-router": ">= ^1.124.0", "react-slick": "^0.30.3", "clsx": ">= 2.1.1", "motion": ">= 12.16.0", "react": ">= 19.1.0", "typescript": ">= 5.8.3", "@legendapp/state": ">= 3.0.0-beta.31", "@headlessui/react": ">= 2.2.4", "react-pdf": ">= 10.0.1", "react-hot-toast": ">= 2.5.2", "arktype": ">= 2.1.20", "dayjs": ">= 1.11.13", "react-day-picker": ">= 9.8.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "type": "module", "version": "0.0.0"}