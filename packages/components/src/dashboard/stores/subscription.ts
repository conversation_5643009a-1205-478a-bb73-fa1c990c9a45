import { ObservableHint, observable } from '@legendapp/state'
import { utcDate } from '@mass/utils'
import { type } from 'arktype'

const queryValidation = type({
  period: "'yearly'",
  selectedDate: type('Date').array().atMostLength(2).atLeastLength(1),
  isRange: 'false',
  isLastX: 'false',
}).or(
  type({
    period: "'monthly' | 'daily'",
    selectedDate: type('Date').array().atMostLength(2).atLeastLength(1),
    isRange: 'false',
    isLastX: 'boolean',
  }).or(
    type({
      period: "'monthly' | 'daily'",
      selectedDate: type('Date').array().atMostLength(2).atLeastLength(2),
      isRange: 'boolean',
      isLastX: 'boolean',
    }),
  ),
)

export const ALLOWED_MONTH_COUNT = 5
export const ALLOWED_DAY_COUNT = 10

export const subscription$ = observable<Dashboard.Subscription>({
  regions: null,
  subscriptions: null,

  selectedSubscription: null,

  usageLimits: null,

  query: {
    values: {
      period: 'yearly',
      isRange: false,
      isLastX: false,
      selectedDate: [utcDate().startOf('year').toDate()],
    },

    errors: {
      period: false,
      isRange: false,
      isLastX: false,
      selectedDate: false,
    },

    dirty: {
      period: false,
      isRange: false,
      isLastX: false,
      selectedDate: false,
    },

    setPeriod: ObservableHint.function(period => {
      if (period === 'yearly') {
        subscription$.query.values.isRange.set(false)
        subscription$.query.values.isLastX.set(false)
      }

      let newStartDate: Date

      if (period === 'yearly') {
        newStartDate = utcDate().startOf('year').toDate()
      } else if (period === 'monthly') {
        newStartDate = utcDate().startOf('month').toDate()
      } else {
        newStartDate = utcDate().startOf('day').toDate()
      }

      subscription$.query.values.selectedDate.set([newStartDate])

      subscription$.query.values.period.set(period)
      subscription$.query.setDirty('period')
    }),

    setIsRange: ObservableHint.function(isRange => {
      if (isRange) {
        subscription$.query.values.isLastX.set(false)
      } else {
        subscription$.query.values.selectedDate[1]?.set(null)
      }

      subscription$.query.values.isRange.set(isRange)
      subscription$.query.setDirty('isRange')
    }),

    setIsLastX: ObservableHint.function(isLastX => {
      if (isLastX) {
        subscription$.query.values.isRange.set(false)
      }

      subscription$.query.values.isLastX.set(isLastX)
      subscription$.query.setDirty('isLastX')
    }),

    setSelectedDate: ObservableHint.function(selectedDate => {
      subscription$.query.values.selectedDate.set(selectedDate)
      subscription$.query.setDirty('selectedDate')
    }),

    isError: ObservableHint.function(key => {
      const value = subscription$.query.values[key].get()
      const dirty = subscription$.query.dirty[key].get()

      return dirty && queryValidation(value) instanceof type.errors
    }),
    setDirty: ObservableHint.function(key => {
      subscription$.query.errors[key].set(
        queryValidation.get(key)(subscription$.query.values[key].get()) instanceof type.errors,
      )

      if (queryValidation.get(key)(subscription$.query.values[key].get()) instanceof type.errors) {
        console.log(queryValidation.get(key)(subscription$.query.values[key].get()))
      }

      subscription$.query.dirty[key].set(true)
    }),

    startYearOptions: () => {
      // biome-ignore lint/style/noNonNullAssertion: Redundant
      const max = subscription$.usageLimits.max.year.get()!

      return Array.from(new Array(max), (_el, index) => utcDate().subtract(index, 'year').startOf('year').toDate())
    },

    startMonthOptions: () => {
      // biome-ignore lint/style/noNonNullAssertion: Redundant
      const max = subscription$.usageLimits.max.month.get()!

      return Array.from(new Array(max), (_el, index) => utcDate().subtract(index, 'month').startOf('month').toDate())
    },
    endMonthOptions: () => {
      const startMonthOptions = subscription$.query.startMonthOptions.get()

      const startDate = subscription$.query.values.selectedDate.get().at(0)

      if (!startDate) {
        return []
      }

      const indexOfSelected = startMonthOptions.findIndex(
        date => date.getFullYear() === startDate.getFullYear() && date.getMonth() === startDate.getMonth(),
      )

      const remainingMonths = startMonthOptions.slice(0, indexOfSelected)

      if (remainingMonths.length > ALLOWED_MONTH_COUNT) {
        return remainingMonths.slice(remainingMonths.length - ALLOWED_MONTH_COUNT)
      }

      return remainingMonths
    },

    populated: () => {
      const { period, isRange, isLastX, selectedDate } = subscription$.query.values.get()

      let granularity: Api.ExtractParams<Api.Services['subscriptions']['usage']['data']>['granularity'] = 'month'

      if (period === 'yearly') {
        granularity = 'month'
      } else if (period === 'monthly') {
        granularity = isRange || isLastX ? 'month' : 'day'
      } else if (period === 'daily') {
        granularity = isRange || isLastX ? 'day' : 'hour'
      }

      let rawStartDate = selectedDate[0]
      let rawEndDate = selectedDate[1]

      if (isLastX && period !== 'yearly') {
        const prevName = period === 'monthly' ? 'month' : 'day'
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        const prev = subscription$.usageLimits.prev[prevName].get()!

        rawEndDate = rawStartDate
        rawStartDate = utcDate(rawEndDate).subtract(prev, prevName).toDate()
      }

      if (!selectedDate[0]) {
        return null
      }

      const startDate = utcDate(rawStartDate).startOf(granularity).toISOString()
      const endDate = (rawEndDate ? utcDate(rawEndDate) : utcDate(rawStartDate).add(1, granularity))
        .startOf(granularity)
        .toISOString()

      return {
        startDate,
        endDate,
        granularity,
      }
    },

    clear: ObservableHint.function(() => {
      subscription$.query.values.set({
        period: 'yearly',
        isRange: false,
        isLastX: false,
        selectedDate: [utcDate().startOf('year').toDate()],
      })

      subscription$.query.errors.set({
        period: false,
        isRange: false,
        isLastX: false,
        selectedDate: false,
      })

      subscription$.query.dirty.set({
        period: false,
        isRange: false,
        isLastX: false,
        selectedDate: false,
      })
    }),
  },

  usage: {
    data: null,
    isLoading: false,

    clear: ObservableHint.function(() => {
      subscription$.usage.data.set(null)
      subscription$.usage.isLoading.set(false)
    }),
  },
})
