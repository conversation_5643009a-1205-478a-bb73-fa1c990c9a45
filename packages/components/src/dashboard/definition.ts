import type { ArrayValue, PlainObject } from '@legendapp/state'
import type { Type } from 'arktype'

declare global {
  namespace Dashboard {
    type Modals =
      | 'subscription-filters'
      | 'new-subscription'
      | 'update-subscription'
      | `document-${keyof Api.Stores.Global['aggreements']}`
      | 'test'

    type SubscriptionParams = Api.ExtractParams<Api.Services['subscriptions']['list']>

    interface SubscriptionFilters {
      params: SubscriptionParams

      clearFilters: () => void

      eq: PlainObject<
        <
          T extends keyof ArrayValue<SubscriptionParams['filter:eq']>,
          const D,
          R = ArrayValue<SubscriptionParams['filter:eq']>[T] | D,
        >(
          type: T,
          options: {
            defaultValue: D
            removeIfDefault?: boolean
          },
        ) => {
          value: Exclude<R | D, undefined>
          set: (value: R) => void
        }
      >
    }

    type NewSubscriptionPayload = Api.ExtractPayload<Api.Services['subscriptions']['create']>

    interface NewSubscription {
      payload: Partial<NewSubscriptionPayload>
      errors: Record<keyof NewSubscriptionPayload, boolean>
      dirty: Record<keyof NewSubscriptionPayload, boolean>

      clearPayload: PlainObject<() => void>

      use: PlainObject<
        <T extends keyof NewSubscriptionPayload>(
          type: T,
          checker?: Type<NewSubscriptionPayload[T]>,
        ) => {
          value: NewSubscriptionPayload[T]
          error: boolean
          set: (value: NewSubscriptionPayload[T]) => void
          setDirty: () => void
        }
      >
    }

    type UpdateSubscriptionPayload = Api.ExtractPayload<Api.Services['subscriptions']['update']>

    interface UpdateSubscription {
      original: ArrayValue<Api.ExtractResponse<Api.Services['subscriptions']['list']>['content']> | null
      payload: Partial<UpdateSubscriptionPayload>
      errors: Record<keyof UpdateSubscriptionPayload, boolean>
      dirty: Record<keyof UpdateSubscriptionPayload, boolean>

      clearPayload: PlainObject<() => void>

      use: PlainObject<
        <T extends keyof UpdateSubscriptionPayload>(
          type: T,
          checker?: Type<UpdateSubscriptionPayload[T]>,
        ) => {
          value: UpdateSubscriptionPayload[T]
          error: boolean
          set: (value: UpdateSubscriptionPayload[T]) => void
          setDirty: () => void
        }
      >
    }

    type Periods = 'yearly' | 'monthly' | 'daily'

    interface Subscription {
      regions: Api.ExtractResponse<Api.Services['subscriptions']['regions']> | null
      subscriptions: Api.ExtractResponse<Api.Services['subscriptions']['list']> | null

      selectedSubscription: ArrayValue<Api.ExtractResponse<Api.Services['subscriptions']['list']>['content']> | null

      usageLimits: {
        max: {
          year: number
          month: number
          day: number
        }
        prev: {
          day: number
          month: number
        }
      } | null

      query: {
        startYearOptions: Date[]

        startMonthOptions: Date[]
        endMonthOptions: Date[]

        values: {
          period: Dashboard.Periods
          isRange: boolean
          isLastX: boolean
          selectedDate: (Date | null)[]
        }

        populated: Api.ExtractParams<Api.Services['subscriptions']['usage']['data']> | null

        errors: Record<keyof Dashboard.Subscription['query']['values'], boolean>
        dirty: Record<keyof Dashboard.Subscription['query']['values'], boolean>

        setPeriod: PlainObject<(period: Dashboard.Periods) => void>
        setIsRange: PlainObject<(isRange: boolean) => void>
        setIsLastX: PlainObject<(isLastX: boolean) => void>
        setSelectedDate: PlainObject<(selectedDate: (Date | null)[]) => void>

        isError: PlainObject<(key: keyof Dashboard.Subscription['query']['values']) => void>
        setDirty: PlainObject<(key: keyof Dashboard.Subscription['query']['values']) => void>

        clear: PlainObject<() => void>
      }

      usage: {
        data: Api.ExtractResponse<Api.Services['subscriptions']['usage']['data']> | null
        isLoading: boolean

        clear: PlainObject<() => void>
      }
    }
  }
}
