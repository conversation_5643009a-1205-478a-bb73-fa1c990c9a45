import { Memo, use$ } from '@legendapp/state/react'
import { FilterLinesIcon, PlusIcon } from '@mass/icons'
import { useLocation } from '@tanstack/react-router'
import clsx from 'clsx'
import type { FC } from 'react'
import { useTranslation } from 'react-i18next'

import { Badge, Button, type LineTabItem, LineTabs, Text, Title, ui$ } from '../../../shared'
import { useMeta } from '../../hooks/use-meta'
import { subscription$ } from '../../stores/subscription'
import { Breadcrumbs } from '../shared/breadcrumbs'

const SubscriptionTabs: FC = () => {
  const { t: dashboard } = useTranslation('dashboard')
  const subscription = use$(() => subscription$.selectedSubscription.get())

  const tabs: LineTabItem[] = [
    {
      id: 'details',
      label: dashboard('subscriptions.details'),
      to: '/$subscriptionId/',
      params: { subscriptionId: subscription?.id },
    },
    {
      id: 'query',
      label: dashboard('subscriptions.query'),
      to: '/$subscriptionId/query',
      params: { subscriptionId: subscription?.id },
    },
    {
      id: 'outages',
      label: dashboard('subscriptions.outages'),
      to: '/$subscriptionId/outages',
      params: { subscriptionId: subscription?.id },
    },
    {
      id: 'notifications',
      label: dashboard('subscriptions.notifications'),
      to: '/$subscriptionId/notifications',
      params: { subscriptionId: subscription?.id },
    },
  ]

  return <LineTabs tabs={tabs} className='mt-8' />
}

const HomeHeaderActions: FC = () => {
  const { t: common } = useTranslation('common')

  return (
    <div className='flex gap-4'>
      <Button
        variant='bordered'
        className='rounded-c1'
        onClick={() => ui$.onChangeModal('dashboard.subscription-filters', true)}>
        <Text variant='dim-2-medium'> {common('filter')} </Text>
        <FilterLinesIcon strokeWidth={1.5} />
      </Button>

      <Button
        variant='primary'
        className='rounded-c1'
        onClick={() => ui$.onChangeModal('dashboard.new-subscription', true)}>
        <Text variant='white' className='text-nowrap'>
          {common('add-new')}
        </Text>
        <PlusIcon strokeWidth={1.5} />
      </Button>
    </div>
  )
}

export const Header: FC = () => {
  const { pathname } = useLocation()
  const { t: dashboard } = useTranslation('dashboard')

  const { title, description, hasTabs } = useMeta()

  const showBadge = pathname === '/'

  return (
    <header
      className={clsx(
        'flex flex-col gap-16', // flex
        'h-auto w-full max-w-screen', // sizing
        'px-8 pt-8 md:px-16 md:pt-16', // padding
        'border-accessory-1 border-b', // accessory
        {
          'pb-8 md:pb-16': !hasTabs,
        },
      )}>
      <Breadcrumbs />

      <div
        className={clsx(
          'flex flex-col justify-between gap-12 md:flex-row md:items-end md:gap-4', // flex
        )}>
        <div
          className={clsx(
            'flex flex-row', // flex
          )}>
          <div
            className={clsx(
              'flex flex-col gap-4', // flex
            )}>
            <div className='flex items-center gap-8'>
              <Title> {title} </Title>
              {showBadge && (
                <Badge slim>
                  <Memo>
                    {() =>
                      dashboard('subscriptions.x-many-subscriptions', {
                        count: subscription$.subscriptions.get()?.content.length ?? 0,
                      })
                    }
                  </Memo>
                </Badge>
              )}
            </div>
            <Text variant='dim-2'> {description} </Text>

            {pathname !== '/' && hasTabs && <SubscriptionTabs />}
          </div>
        </div>

        {pathname === '/' && <HomeHeaderActions />}
      </div>
    </header>
  )
}
