import clsx from 'clsx'
import type { FC } from 'react'

export interface ButtonStylesProps {
  variant?:
    | 'custom'
    | 'icon'
    | 'primary'
    | 'bordered'
    | 'hover-slim'
    | 'hover-error'
    | 'error'
    | 'error-slim'
    | undefined
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useButtonStyles = ({ className, variant }: ButtonStylesProps) =>
  clsx(
    'flex items-center justify-center gap-6',
    'cursor-pointer rounded-b1', // styling
    'outline-primary',
    'text-black text-xs', // text
    'transition-colors duration-200', // animation
    'disabled:cursor-not-allowed disabled:opacity-40',
    {
      'w-full px-8 py-5':
        variant !== 'custom' && variant !== 'icon' && variant !== 'hover-slim' && variant !== 'error-slim',
      'bg-primary text-white text-base font-semibold outline-black/50! hover:bg-primary/90': variant === 'primary',
      'bg-error text-white text-base font-semibold outline-black/50! hover:bg-error/90': variant === 'error',
      'bg-error text-white text-base font-semibold outline-black/50! hover:bg-error/90 px-6 py-3 w-full':
        variant === 'error-slim',
      'hover:bg-error hover:text-white text-base font-semibold bg-whity/10 outline-black/50!':
        variant === 'hover-error',
      'border border-accessory-1 hover:bg-black/5 bg-white': variant === 'bordered' || variant === 'hover-error',
      'p-4': variant === 'icon',
      'hover:bg-black/5 px-6 py-3 w-full': variant === 'hover-slim',
    }, // variants
    className,
  )

export const Button: FC<
  React.ButtonHTMLAttributes<HTMLButtonElement> &
    ButtonStylesProps & {
      children: React.ReactNode
    }
> = ({ className, variant, children, ...props }) => {
  const styles = useButtonStyles({ variant, className })

  return (
    <button type='button' className={styles} {...props}>
      {children}
    </button>
  )
}
