/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import 'react-day-picker/style.css'

import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react'
import { CalendarIcon, ChevronDownIcon } from '@mass/icons'
import { type BlobType, i18n, useDate } from '@mass/utils'
import clsx from 'clsx'
import { AnimatePresence } from 'motion/react'
import { div } from 'motion/react-client'
import type { FC } from 'react'
import { DayPicker } from 'react-day-picker'
import { enUS, tr } from 'react-day-picker/locale'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { Button } from './button'
import { Input, type InputStylesProps } from './input'

const DatePickerInput: FC<{
  ref: React.Ref<HTMLInputElement>
  className?: string
  value?: string | undefined
  placeholder?: string | undefined
  disabled?: boolean
  error?: boolean
}> = ({ ref, className, value, placeholder, disabled, error, ...props }) => {
  return (
    <Input
      ref={ref}
      readOnly
      placeholder={placeholder}
      defaultValue={value ?? ''}
      error={error ?? false}
      className={clsx('pr-20', className)}
      {...props}
      disabled={disabled}
    />
  )
}

export interface DatePickerProps extends Omit<InputStylesProps, 'variant'> {
  mode?: 'single' | 'range'
  value: (Date | null)[]
  onChange?: (date: (Date | null)[]) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  error?: boolean
  format?: string

  min?: number | undefined
  max?: number | undefined

  minDate?: Date | undefined
  maxDate?: Date | undefined
}

export const DatePicker: FC<DatePickerProps> = ({
  mode = 'single',
  value,
  onChange,
  className,
  placeholder,
  disabled = false,
  error = false,
  format = 'DD MMMM YYYY',

  min,
  max,

  minDate,
  maxDate,
}) => {
  const { t: common } = useTranslation('common')
  const formatDate = useDate(format)

  // Convert value to Date if it's a string
  const dateValue = value ? value.map(date => (typeof date === 'string' ? new Date(date) : date)) : []

  // Format date for display
  const displayValue = dateValue
    ? dateValue
        .filter(Boolean)
        .map(dateValueItem => (dateValueItem ? formatDate(dateValueItem) : ''))
        .join(' - ')
    : ''

  return (
    <div className={clsx('relative w-full', className)}>
      <Popover className='relative'>
        {({ open }) => (
          <>
            <div className='relative w-full'>
              <PopoverButton
                as={DatePickerInput}
                value={displayValue}
                placeholder={placeholder}
                error={error}
                disabled={disabled}
              />

              <CalendarIcon
                strokeWidth={2}
                className={clsx(
                  '-translate-y-1/2 pointer-events-none absolute top-1/2 right-8 h-8 w-8', // positioning
                  'text-dim-3 transition-colors duration-200', // color
                  {
                    'cursor-not-allowed text-dim-3 opacity-30': disabled,
                    'text-primary': open && !disabled,
                  },
                )}
              />
            </div>

            <AnimatePresence>
              {open && (
                <PopoverPanel
                  static
                  as={div}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  anchor='bottom'
                  className={clsx(
                    'z-50 mt-1 origin-top',
                    'rounded-c1 border border-accessory-1 bg-white p-4 shadow-layer-1',
                    '[--anchor-gap:4px]',
                  )}>
                  <DayPicker
                    animate
                    required={false}
                    mode={mode}
                    locale={i18n.language === 'tr' ? tr : enUS}
                    components={{
                      // biome-ignore lint/style/useNamingConvention: Redundant
                      // biome-ignore lint/nursery/noNestedComponentDefinitions: Redundant
                      NextMonthButton: ({ className, ...props }) => (
                        <Button variant='icon' className={clsx(className, '-rotate-90')} {...props}>
                          <ChevronDownIcon className='h-12 w-12 text-primary hover:text-primary/90' />
                        </Button>
                      ),
                      // biome-ignore lint/style/useNamingConvention: Redundant
                      // biome-ignore lint/nursery/noNestedComponentDefinitions: Redundant
                      PreviousMonthButton: ({ className, ...props }) => (
                        <Button variant='icon' className={clsx(className, 'rotate-90')} {...props}>
                          <ChevronDownIcon className='h-12 w-12 text-primary hover:text-primary/90' />
                        </Button>
                      ),
                    }}
                    selected={
                      (mode === 'single'
                        ? dateValue[0]
                        : {
                            from: dateValue[0],
                            to: dateValue[1],
                          }) as BlobType
                    }
                    // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Redundant
                    onSelect={(dates: BlobType) => {
                      if (mode === 'single') {
                        return onChange?.(dates ? [dates] : [null])
                      }

                      if (!dates) {
                        return toast.error(common('select-range'))
                      }

                      let differentOne = dates.from && value[0] !== dates.from ? 'from' : 'to'

                      let newValue = value
                      let isNew = !value[0]

                      if (value[0] && value[1]) {
                        isNew = true

                        if (!dates[differentOne]) {
                          if (differentOne === 'to') {
                            differentOne = 'from'
                          } else if (differentOne === 'from') {
                            differentOne = 'to'
                          }
                        }

                        newValue = [dates[differentOne], null]
                      } else {
                        newValue = [dates.from, dates.to]
                      }

                      if (!(newValue[1] || isNew)) {
                        return toast.error(common('select-range-x-to-y', { x: min ?? 0, y: max }))
                      }

                      onChange?.(newValue)
                    }}
                    ISOWeek
                    navLayout='around'
                    disabled={
                      Object.assign(minDate ? { before: minDate } : {}, maxDate ? { after: maxDate } : {}) as BlobType
                    }
                    {...(min ? { min } : {})}
                    {...(max ? { max } : {})}
                    classNames={{
                      today: clsx('bg-accessory-1 text-black'),
                      selected: clsx('bg-primary text-white'),
                      // biome-ignore lint/style/useNamingConvention: Redundant
                      range_start: clsx('bg-success hover:bg-success/90! text-white'),
                      // biome-ignore lint/style/useNamingConvention: Redundant
                      range_end: clsx('bg-secondary hover:bg-secondary/90! text-white'),
                      // biome-ignore lint/style/useNamingConvention: Redundant
                      range_middle: clsx('bg-primary hover:bg-primary/90! text-white'),
                      day: clsx(
                        'rounded-b2 text-xs',
                        'transition-colors duration-200',
                        'hover:bg-black/5 data-selected:hover:bg-primary/90',
                      ),
                      weekdays: clsx('text-dim-3'),
                      // biome-ignore lint/style/useNamingConvention: Redundant
                      month_grid: clsx('border-spacing-1 border-separate'),
                      // biome-ignore lint/style/useNamingConvention: Redundant
                      caption_label: clsx('text-black text-sm my-auto'),
                    }}
                  />
                </PopoverPanel>
              )}
            </AnimatePresence>
          </>
        )}
      </Popover>
    </div>
  )
}
