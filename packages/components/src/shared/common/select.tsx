import { SelectorVerticalIcon } from '@mass/icons'
import type { BlobType } from '@mass/utils'
import {
  Arrow,
  Content,
  Icon,
  Item,
  ItemText,
  Portal,
  Root,
  ScrollDownButton,
  ScrollUpButton,
  Trigger,
  Value,
  Viewport,
} from '@radix-ui/react-select'
import clsx from 'clsx'
import { type FC, useState } from 'react'

import { useButtonStyles } from './button'

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export const Select: FC<{
  placeholder?: string
  options: SelectOption[]
  onValueChange?: (value: BlobType) => void
  value?: string | undefined
  defaultValue?: string
  disabled?: boolean
  children?: React.ReactNode
  className?: string
  defaultOpen?: boolean
  error?: boolean
}> = ({
  placeholder = 'Select an option...',
  options,
  defaultValue,
  onValueChange,
  value,
  disabled,
  className,
  defaultOpen = false,
  error = false,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const selectedOption = options.find(option => option.value === value)

  const rootProps = {
    ...(onValueChange && { onValueChange }),
    ...(typeof value === 'string' && { value }),
    ...(defaultValue && { defaultValue }),
    ...(disabled !== undefined && { disabled }),
    open: true,
    onOpenChange: setIsOpen,
    ...props,
  }

  return (
    <Root {...rootProps}>
      <Trigger
        className={clsx(
          useButtonStyles({ variant: 'bordered' }), // variants
          'justify-between! bg-white', // styles
          'text-nowrap',
          'data-placeholder:text-dim-3',
          {
            'border-error': error,
          },
          className,
        )}>
        <Value placeholder={placeholder} className='text-nowrap'>
          {selectedOption?.label}
        </Value>
        <Icon>
          <SelectorVerticalIcon
            className={clsx(
              'h-8 w-8 text-dim-3', // sizing
              'transition-transform duration-300', // animation
              {
                '-rotate-90': isOpen,
              },
            )}
          />
        </Icon>
      </Trigger>

      <Portal>
        {/* TODO: animation */}
        <Content
          className={clsx(
            'relative z-[9999] overflow-hidden',
            'max-h-[400px] min-w-[200px]',
            'rounded-b1 bg-white shadow-layer-1',
            'border border-accessory-1',
            'data-[side=left]:-translate-x-1 data-[side=top]:-translate-y-1 data-[side=right]:translate-x-1 data-[side=bottom]:translate-y-1',
          )}
          position='popper'
          side='bottom'
          align='center'
          sideOffset={4}>
          <ScrollUpButton />
          <Viewport className='h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'>
            {options.map(option => (
              <Item
                key={option.value}
                value={option.value as BlobType}
                disabled={option.disabled ?? false}
                className={clsx(
                  'relative', // positioning
                  'flex items-center justify-between', // flex
                  'px-8 py-3', // spacing
                  'text-black text-xs',
                  'cursor-pointer select-none outline-none transition-colors duration-200', // other
                  {
                    'hover:bg-black/5 focus:bg-black/5': !option.disabled,
                    'cursor-not-allowed opacity-50': option.disabled,
                  },
                )}>
                {option.value === value && <span className='absolute inset-y-0 left-0 w-2 bg-primary' />}
                <ItemText>{option.label}</ItemText>
              </Item>
            ))}
          </Viewport>
          <ScrollDownButton />
          <Arrow />
        </Content>
      </Portal>
    </Root>
  )
}
