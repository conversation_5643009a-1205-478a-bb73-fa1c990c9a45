import { CheckIcon } from '@mass/icons'
import clsx from 'clsx'
import type { FC } from 'react'
import { forwardRef, useId } from 'react'
import { Text } from './text'

interface CheckboxStylesProps {
  variant?: 'bordered'
  className?: string | undefined
  error?: boolean | undefined
  disabled?: boolean | undefined
}

const useCheckboxStyles = ({ className, variant = 'bordered', error, disabled }: CheckboxStylesProps) =>
  clsx(
    'relative inline-flex items-center justify-center',
    'cursor-pointer rounded-[6px] h-10 w-10', // styling - same as radio
    'outline-primary border',
    'transition-all duration-200', // animation
    'focus-visible:outline-2 focus-visible:outline-offset-2',
    {
      'cursor-not-allowed opacity-50': disabled,
      'bg-white border-accessory-1 hover:border-primary': variant === 'bordered' && !error,
      'border-error': error,
    },
    className,
  )

interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'disabled'>, CheckboxStylesProps {
  label?: string
}

const Checkbox: FC<CheckboxProps> = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, variant = 'bordered', error = false, label, checked, disabled, ...props }, ref) => {
    const checkboxStyles = useCheckboxStyles({ variant, className, error, disabled })
    const id = useId()

    return (
      <div className='flex items-center gap-3'>
        <div className='relative flex items-center'>
          <input
            ref={ref}
            id={id}
            type='checkbox'
            className='sr-only'
            checked={checked}
            disabled={disabled}
            {...props}
          />
          <label
            htmlFor={id}
            className={clsx(checkboxStyles, {
              'border-primary': checked && !disabled,
              'border-dim-3': checked && disabled,
            })}>
            {checked && (
              <CheckIcon
                strokeWidth='6'
                className={clsx(
                  'h-6 w-6 rounded-[4px]', // sizing
                  'transition-all duration-200', // animation
                  {
                    'text-primary': !disabled,
                    'text-dim-3': disabled,
                  },
                )}
              />
            )}
          </label>
        </div>

        {label && (
          <Text
            el='label'
            variant='dim-2'
            // @ts-expect-error
            htmlFor={id}
            className={clsx('cursor-pointer font-medium text-xs', {
              'text-dim-3': disabled,
              'text-error': error,
            })}>
            {label}
          </Text>
        )}
      </div>
    )
  },
)

Checkbox.displayName = 'Checkbox'

export { Checkbox }
