import utc from 'dayjs/plugin/utc'
import 'dayjs/locale/tr'
import 'dayjs/locale/en'

// biome-ignore lint/style/noExportedImports: Redundant
import dayjs from 'dayjs'

dayjs.extend(utc)

import { i18n } from '../i18n'

export const utcDate = (value?: string | null | Date) => dayjs(value?.toString()).utcOffset(0)

export const useDate = (dateFormat: string) => {
  return (value: string | Date) => utcDate(value.toString()).locale(i18n.language).format(dateFormat)
}

export { dayjs }
