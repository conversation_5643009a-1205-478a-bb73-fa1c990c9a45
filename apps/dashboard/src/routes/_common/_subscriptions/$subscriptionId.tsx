import { subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { createFileRoute, Outlet } from '@tanstack/react-router'

function RouteComponent() {
  return <Outlet />
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId')({
  async beforeLoad({ params }) {
    const subscriptionId = params.subscriptionId

    const subscription = await subscriptionsApi.detail({
      query: {
        id: subscriptionId,
      },
    })

    subscription$.selectedSubscription.set(subscription)
  },

  component: RouteComponent,
})
