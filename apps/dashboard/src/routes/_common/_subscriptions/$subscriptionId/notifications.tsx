import { use$ } from '@legendapp/state/react'
import { subscription$ } from '@mass/components/dashboard'
import { Button, Input, Select, Title } from '@mass/components/shared'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

const MAX_THRESHOLD_OPTIONS = Array.from({
  length: 20,
}).map((_, index) => ({
  value: `${(100 + (index + 1) * 5) / 100}`, // 1 to 2
  label: `${(index + 1) * 5}`,
}))

console.log(MAX_THRESHOLD_OPTIONS)

function SubscriptionNotifications() {
  const { t: common } = useTranslation('common')

  const userDefinedLimitBase = use$(() => subscription$.selectedSubscription.userDefinedLimit.get() ?? 0)
  const [userDefinedLimit, setUserDefinedLimit] = useState(`${userDefinedLimitBase}`)

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:gap-16 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-row items-center justify-between', // flex
          'gap-4 md:gap-6', // spacing
          'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('user-defined-limit')}
        </Title>

        <Input
          type='number'
          value={userDefinedLimit}
          onChange={e => setUserDefinedLimit(e.target.value)}
          className='max-w-[350px]'
          max='1000'
          min='0'
          onBlur={e => {
            const numberified = Number(e.target.value)

            if (numberified < 0) {
              setUserDefinedLimit('0')
            }

            if (numberified > 1000) {
              setUserDefinedLimit('1000')
            }
          }}
        />
      </div>

      <div
        className={clsx(
          'flex flex-row items-center justify-between', // flex
          'gap-4 md:gap-6', // spacing
          'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('unexpected-overconsumption-threshold')}
        </Title>

        <Select
          value={userDefinedLimit}
          options={MAX_THRESHOLD_OPTIONS}
          onValueChange={setUserDefinedLimit}
          className='max-w-[350px]'
        />
      </div>

      <div
        className={clsx(
          'flex flex-row items-center justify-end', // flex
          'gap-4 md:gap-6', // spacing
          'max-w-[1200px]',
        )}>
        <Button variant='primary' className='w-max rounded-c1'>
          {common('apply')}
        </Button>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/notifications')({
  component: SubscriptionNotifications,
})
